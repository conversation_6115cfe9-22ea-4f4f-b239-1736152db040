<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医务管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f9;
        }
        .layout {
            display: flex;
            min-height: 100vh;
        }
        .sidebar {
            width: 220px;
            background: #283046;
            color: #fff;
            display: flex;
            flex-direction: column;
            padding-top: 20px;
        }
        .sidebar h2 {
            font-size: 1.3em;
            text-align: center;
            margin-bottom: 30px;
            font-weight: 700;
            letter-spacing: 2px;
        }
        .sidebar ul {
            list-style: none;
            padding: 0;
        }
        .sidebar ul li {
            padding: 15px 30px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .sidebar ul li:hover, .sidebar ul li.active {
            background: #7367f0;
        }
        .main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        .header {
            background-color: #fff;
            color: #283046;
            padding: 0 30px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 8px rgba(44,62,80,0.04);
        }
        .header .logo {
            font-size: 1.5em;
            font-weight: bold;
            letter-spacing: 2px;
        }
        .header .user {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        .dashboard {
            display: flex;
            flex-wrap: wrap;
            padding: 30px;
            gap: 24px;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 24px 28px;
            flex: 1;
            min-width: 240px;
            box-shadow: 0 2px 8px rgba(44,62,80,0.08);
            transition: box-shadow 0.2s;
        }
        .card:hover {
            box-shadow: 0 4px 16px rgba(115,103,240,0.15);
        }
        @media (max-width: 900px) {
            .dashboard {
                flex-direction: column;
            }
            .sidebar {
                width: 100px;
                font-size: 0.9em;
            }
        }
        .hidden { display: none !important; }
        .patients-table {
            width: 100%;
            border-collapse: collapse;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(44,62,80,0.08);
            margin-bottom: 24px;
        }
        .patients-table th, .patients-table td {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            text-align: left;
        }
        .patients-table th {
            background: #f8f8fc;
            font-weight: 600;
        }
        .patients-table tr:last-child td {
            border-bottom: none;
        }
        .patients-actions button {
            background: #7367f0;
            color: #fff;
            border: none;
            border-radius: 4px;
            padding: 6px 14px;
            margin-right: 8px;
            cursor: pointer;
            transition: background 0.2s;
        }
        .patients-actions button:hover {
            background: #5b53c2;
        }
    </style>
</head>
<body>
    <div class="layout">
        <aside class="sidebar">
            <h2>医务管理</h2>
            <ul id="menu">
                <li class="active">仪表盘</li>
                <li>患者管理</li>
                <li>医生管理</li>
                <li>预约管理</li>
                <li>就诊记录</li>
                <li>药品管理</li>
                <li>财务管理</li>
                <li>系统设置</li>
            </ul>
        </aside>
        <div class="main">
            <div class="header">
                <span class="logo">医务管理系统</span>
                <div class="user">
                    <span>通知</span>
                    <span>管理员</span>
                    <img src="https://i.pravatar.cc/32?img=3" alt="avatar" style="border-radius:50%;width:32px;height:32px;">
                </div>
            </div>
            <div id="dashboard" class="dashboard">
                <div class="card">
                    <h2>患者信息</h2>
                    <p>今日新增患者: 12</p>
                    <p>总患者数: 1245</p>
                </div>
                <div class="card">
                    <h2>医生排班</h2>
                    <p>今日值班医生: 8</p>
                    <p>本周预约: 45</p>
                </div>
                <div class="card">
                    <h2>药品库存</h2>
                    <p>即将过期药品: 3</p>
                    <p>库存不足药品: 5</p>
                </div>
                <div class="card">
                    <h2>财务统计</h2>
                    <p>今日收入: ¥2,300</p>
                    <p>本月支出: ¥8,900</p>
                </div>
            </div>
            <div id="dashboard-desc" style="padding: 0 30px 30px 30px;">
                <h3>系统简介</h3>
                <p>欢迎使用医务管理系统。您可以通过左侧菜单进入各功能模块，进行患者、医生、预约、药品、财务等管理。</p>
            </div>
            <div id="patients" class="hidden" style="padding: 30px;">
                <h2>患者管理</h2>
                <table class="patients-table">
                    <thead>
                        <tr>
                            <th>姓名</th>
                            <th>性别</th>
                            <th>年龄</th>
                            <th>联系方式</th>
                            <th>注册时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>张三</td>
                            <td>男</td>
                            <td>32</td>
                            <td>138****8888</td>
                            <td>2025-06-20</td>
                            <td class="patients-actions">
                                <button>详情</button>
                                <button>编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>李四</td>
                            <td>女</td>
                            <td>27</td>
                            <td>139****6666</td>
                            <td>2025-06-28</td>
                            <td class="patients-actions">
                                <button>详情</button>
                                <button>编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <button style="background:#28c76f;color:#fff;border:none;padding:10px 24px;border-radius:4px;cursor:pointer;">新增患者</button>
            </div>
            <div id="doctors" class="hidden" style="padding: 30px;">
                <h2>医生管理</h2>
                <table class="patients-table">
                    <thead>
                        <tr>
                            <th>姓名</th>
                            <th>科室</th>
                            <th>职称</th>
                            <th>联系方式</th>
                            <th>排班</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>王主任</td>
                            <td>内科</td>
                            <td>主任医师</td>
                            <td>137****1234</td>
                            <td>周一、三、五</td>
                            <td class="patients-actions">
                                <button>详情</button>
                                <button>编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>赵医生</td>
                            <td>外科</td>
                            <td>主治医师</td>
                            <td>136****5678</td>
                            <td>周二、四</td>
                            <td class="patients-actions">
                                <button>详情</button>
                                <button>编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <button style="background:#28c76f;color:#fff;border:none;padding:10px 24px;border-radius:4px;cursor:pointer;">新增医生</button>
            </div>
            <div id="physicians" class="hidden" style="padding: 30px;">
                <h2>医师管理</h2>
                <table class="patients-table">
                    <thead>
                        <tr>
                            <th>姓名</th>
                            <th>专业</th>
                            <th>执业证号</th>
                            <th>联系方式</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>孙医师</td>
                            <td>儿科</td>
                            <td>YS2025001</td>
                            <td>135****8888</td>
                            <td>在岗</td>
                            <td class="patients-actions">
                                <button>详情</button>
                                <button>编辑</button>
                            </td>
                        </tr>
                        <tr>
                            <td>周医师</td>
                            <td>口腔科</td>
                            <td>YS2025002</td>
                            <td>134****6666</td>
                            <td>休假</td>
                            <td class="patients-actions">
                                <button>详情</button>
                                <button>编辑</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <button style="background:#28c76f;color:#fff;border:none;padding:10px 24px;border-radius:4px;cursor:pointer;">新增医师</button>
            </div>
        </div>
    </div>
    <script>
        // 左侧菜单切换内容
        const menu = document.getElementById('menu');
        const dashboard = document.getElementById('dashboard');
        const dashboardDesc = document.getElementById('dashboard-desc');
        const patients = document.getElementById('patients');
        const doctors = document.getElementById('doctors');
        const physicians = document.getElementById('physicians');
        menu.addEventListener('click', function(e) {
            if(e.target.tagName === 'LI') {
                Array.from(menu.children).forEach(li => li.classList.remove('active'));
                e.target.classList.add('active');
                // 隐藏所有模块
                dashboard.classList.add('hidden');
                dashboardDesc.classList.add('hidden');
                patients.classList.add('hidden');
                doctors.classList.add('hidden');
                physicians.classList.add('hidden');
                // 根据菜单显示对应模块
                if(e.target.textContent === '仪表盘') {
                    dashboard.classList.remove('hidden');
                    dashboardDesc.classList.remove('hidden');
                } else if(e.target.textContent === '患者管理') {
                    patients.classList.remove('hidden');
                } else if(e.target.textContent === '医生管理') {
                    doctors.classList.remove('hidden');
                } else if(e.target.textContent === '医师管理') {
                    physicians.classList.remove('hidden');
                }
            }
        });
    </script>
</body>
</html>